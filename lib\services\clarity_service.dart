import 'dart:convert';
import 'package:clarity_flutter/clarity_flutter.dart';
import 'package:crypto/crypto.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ClarityService extends GetxController {
  static ClarityService? _instance;
  static ClarityService get instance => _instance ??= ClarityService._();

  ClarityService._();

  // Observable user ID for reactive updates
  final Rx<String?> _currentUserId = Rx<String?>(null);
  String? get currentUserId => _currentUserId.value;

  /// Convert email to base36 format as required by Microsoft Clarity
  /// The userId must be a base-36 string larger than 0 and smaller than 1z141z4
  String emailToBase36(String email) {
    // Create a hash of the email to ensure uniqueness and consistent length
    var bytes = utf8.encode(email.toLowerCase().trim());
    var digest = sha256.convert(bytes);

    // Convert the first 8 bytes of the hash to a BigInt
    var hashBytes = digest.bytes.take(8).toList();
    var hashInt = BigInt.zero;

    for (int i = 0; i < hashBytes.length; i++) {
      hashInt = hashInt +
          (BigInt.from(hashBytes[i]) << (8 * (hashBytes.length - 1 - i)));
    }

    // Ensure the number is positive and within the valid range
    // Microsoft Clarity requires: larger than 0 and smaller than 1z141z4 (base36)
    // 1z141z4 in base36 = 2821109907455 in decimal
    var maxValue = BigInt.parse('1z141z4', radix: 36);
    hashInt = hashInt % maxValue;

    // Ensure it's not zero
    if (hashInt == BigInt.zero) {
      hashInt = BigInt.one;
    }

    // Convert to base36
    return hashInt.toRadixString(36);
  }

  /// Set user ID for Clarity tracking
  void setUserId(String userEmail) {
    final userId = emailToBase36(userEmail);
    _currentUserId.value = userId;
    debugPrint('Clarity user ID set: $userId (from email: $userEmail)');
  }

  /// Clear user ID when user logs out
  void clearUserId() {
    _currentUserId.value = null;
    debugPrint('Clarity user ID cleared');
  }

  /// Get the current Clarity configuration
  ClarityConfig getConfig() {
    return ClarityConfig(
      projectId: "s2707qhfo3",
      userId: _currentUserId.value,
      logLevel: LogLevel.Info,
    );
  }

  /// Create a ClarityWidget with current configuration
  Widget wrapApp(Widget app) {
    return Obx(() {
      final config = getConfig();
      return ClarityWidget(
        key: ValueKey(_currentUserId.value), // Force rebuild when user changes
        app: app,
        clarityConfig: config,
      );
    });
  }
}
